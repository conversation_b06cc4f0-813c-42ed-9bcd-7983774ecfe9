"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(pagebuilder)/content-builder/[...slug]/layout",{

/***/ "(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/Media.tsx":
/*!********************************************************************!*\
  !*** ./src/components/Builder/FieldEditor/regular/Media/Media.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Media: function() { return /* binding */ Media; },\n/* harmony export */   checkArr: function() { return /* binding */ checkArr; },\n/* harmony export */   formatDate: function() { return /* binding */ formatDate; },\n/* harmony export */   formatExt: function() { return /* binding */ formatExt; },\n/* harmony export */   useMediaHandlers: function() { return /* binding */ useMediaHandlers; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/../../node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Image/ImageV2.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/../../node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _media_module_scss__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./media.module.scss */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/media.module.scss\");\n/* harmony import */ var _media_module_scss__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_media_module_scss__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _MediaInfoLayer__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./MediaInfoLayer */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/MediaInfoLayer.tsx\");\n\n\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\nvar formatDate = function(date) {\n    return dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date).format(\"D/M/YYYY\");\n};\nvar formatExt = function(ext) {\n    return ext.replace(\".\", \"\");\n};\nvar checkArr = function(value) {\n    return Array.isArray(value);\n};\n// Custom hook for shared media logic\nvar useMediaHandlers = function(propsValue, setPropsValue, currentMediaIdx, setCurrentMediaIdx, multiple, onChange, field) {\n    _s();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_4__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_6__.PageBuilderContext);\n    var setMediaInfoData = context.setMediaInfoData, setActiveMediaId = context.setActiveMediaId;\n    // Utility functions\n    var createFileInput = function() {\n        var multiple = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        var input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \"image/*,video/*,audio/*,.pdf,.doc,.docx\";\n        input.multiple = multiple;\n        return input;\n    };\n    var validateFileSize = function(file) {\n        var maxSize = 20 * 1024 * 1024 // 20MB\n        ;\n        var fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);\n        if (file.size > maxSize) {\n            return {\n                isValid: false,\n                errorMessage: 'File \"'.concat(file.name, '\" (').concat(fileSizeMB, \"MB) exceeds the 20MB limit.\")\n            };\n        }\n        return {\n            isValid: true\n        };\n    };\n    var fileToMediaProps = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__._)(function(file) {\n            var _file_name_split_pop, url, width, height, dimensions, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        url = URL.createObjectURL(file);\n                        if (!file.type.startsWith(\"image/\")) return [\n                            3,\n                            4\n                        ];\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            getImageDimensions(url)\n                        ];\n                    case 2:\n                        dimensions = _state.sent();\n                        width = dimensions.width;\n                        height = dimensions.height;\n                        return [\n                            3,\n                            4\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.warn(\"Failed to get image dimensions:\", error);\n                        // Default dimensions for images if we can't read them\n                        width = 800;\n                        height = 600;\n                        return [\n                            3,\n                            4\n                        ];\n                    case 4:\n                        return [\n                            2,\n                            {\n                                id: Date.now() + Math.random(),\n                                name: file.name,\n                                url: url,\n                                ext: \".\" + ((_file_name_split_pop = file.name.split(\".\").pop()) === null || _file_name_split_pop === void 0 ? void 0 : _file_name_split_pop.toLowerCase()),\n                                mime: file.type,\n                                size: file.size,\n                                alternativeText: file.name,\n                                width: width,\n                                height: height\n                            }\n                        ];\n                }\n            });\n        });\n        return function fileToMediaProps(file) {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    var getImageDimensions = function(url) {\n        return new Promise(function(resolve, reject) {\n            var img = document.createElement(\"img\");\n            img.onload = function() {\n                resolve({\n                    width: img.naturalWidth,\n                    height: img.naturalHeight\n                });\n            };\n            img.onerror = reject;\n            img.src = url;\n        });\n    };\n    var updatePropsValue = function(newValue) {\n        setPropsValue(newValue);\n    // if (onChange) {\n    // \tonChange({ field: field || 'media', value: newValue as unknown })\n    // }\n    };\n    var handleNextMedia = function() {\n        if (checkArr(propsValue) && propsValue.length > 0) {\n            setCurrentMediaIdx(function(prevIdx) {\n                return prevIdx + 1 < propsValue.length ? prevIdx + 1 : 0;\n            });\n        }\n    };\n    var handlePrevMedia = function() {\n        if (checkArr(propsValue) && propsValue.length > 0) {\n            setCurrentMediaIdx(function(prevIdx) {\n                return prevIdx - 1 >= 0 ? prevIdx - 1 : propsValue.length - 1;\n            });\n        }\n    };\n    var handleAdd = function() {\n        var input = createFileInput(multiple);\n        input.onchange = function() {\n            var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__._)(function(e) {\n                var files, validFiles, invalidFiles, newMediaItems, currentArray, newValue;\n                return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__.__generator)(this, function(_state) {\n                    switch(_state.label){\n                        case 0:\n                            files = e.target.files;\n                            if (!files) return [\n                                2\n                            ];\n                            validFiles = [];\n                            invalidFiles = [];\n                            // Validate each file\n                            Array.from(files).forEach(function(file) {\n                                var validation = validateFileSize(file);\n                                if (validation.isValid) {\n                                    validFiles.push(file);\n                                } else {\n                                    invalidFiles.push(validation.errorMessage || \"File \".concat(file.name, \" is invalid\"));\n                                }\n                            });\n                            // Show error messages for invalid files\n                            if (invalidFiles.length > 0) {\n                                alert(\"The following files were skipped:\\n\\n\".concat(invalidFiles.join(\"\\n\")));\n                            }\n                            if (!(validFiles.length > 0)) return [\n                                3,\n                                2\n                            ];\n                            return [\n                                4,\n                                Promise.all(validFiles.map(fileToMediaProps))\n                            ];\n                        case 1:\n                            newMediaItems = _state.sent();\n                            if (multiple) {\n                                currentArray = checkArr(propsValue) ? propsValue : [];\n                                newValue = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(currentArray).concat((0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(newMediaItems));\n                                updatePropsValue(newValue);\n                            } else {\n                                updatePropsValue(newMediaItems[0]);\n                            }\n                            _state.label = 2;\n                        case 2:\n                            return [\n                                2\n                            ];\n                    }\n                });\n            });\n            return function(e) {\n                return _ref.apply(this, arguments);\n            };\n        }();\n        input.click();\n    };\n    var handleReplace = function() {\n        var input = createFileInput(false);\n        input.onchange = function() {\n            var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__._)(function(e) {\n                var files, validation, newMedia, newArray;\n                return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__.__generator)(this, function(_state) {\n                    switch(_state.label){\n                        case 0:\n                            files = e.target.files;\n                            if (!files || !files[0]) return [\n                                2\n                            ];\n                            validation = validateFileSize(files[0]);\n                            if (!validation.isValid) {\n                                alert(validation.errorMessage || \"File exceeds the 20MB limit.\");\n                                return [\n                                    2\n                                ];\n                            }\n                            return [\n                                4,\n                                fileToMediaProps(files[0])\n                            ];\n                        case 1:\n                            newMedia = _state.sent();\n                            if (multiple && checkArr(propsValue)) {\n                                newArray = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(propsValue);\n                                newArray[currentMediaIdx] = newMedia;\n                                updatePropsValue(newArray);\n                            } else {\n                                updatePropsValue(newMedia);\n                            }\n                            // Update context\n                            setMediaInfoData(newMedia);\n                            return [\n                                2\n                            ];\n                    }\n                });\n            });\n            return function(e) {\n                return _ref.apply(this, arguments);\n            };\n        }();\n        input.click();\n    };\n    var handleDuplicate = function() {\n        var currentMedia = checkArr(propsValue) ? propsValue[currentMediaIdx] : propsValue;\n        if (!currentMedia) return;\n        var duplicatedMedia = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, currentMedia), {\n            id: Date.now() + Math.random(),\n            name: \"The copy of \".concat(currentMedia.name)\n        });\n        if (multiple && checkArr(propsValue)) {\n            var newArray = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(propsValue);\n            newArray.splice(currentMediaIdx + 1, 0, duplicatedMedia);\n            updatePropsValue(newArray);\n        } else {\n            // For single mode, replace current with duplicate\n            updatePropsValue(duplicatedMedia);\n        }\n    };\n    var handleRemove = function() {\n        if (multiple && checkArr(propsValue)) {\n            var newArray = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(propsValue);\n            newArray.splice(currentMediaIdx, 1);\n            // Adjust current index if needed\n            if (currentMediaIdx >= newArray.length && newArray.length > 0) {\n                setCurrentMediaIdx(newArray.length - 1);\n            }\n            // Only close MediaInfoLayer if no media left\n            if (newArray.length === 0) {\n                setActiveMediaId(null);\n                setMediaInfoData({\n                    name: \"\",\n                    url: \"\"\n                });\n            } else {\n                // Update mediaInfoData to the new current media\n                var newCurrentIdx = currentMediaIdx >= newArray.length ? newArray.length - 1 : currentMediaIdx;\n                setMediaInfoData(newArray[newCurrentIdx]);\n            }\n            updatePropsValue(newArray);\n        } else {\n            // For single mode, clear the media and close MediaInfoLayer\n            setActiveMediaId(null);\n            setMediaInfoData({\n                name: \"\",\n                url: \"\"\n            });\n            updatePropsValue(null);\n        }\n    };\n    var handleDownload = function() {\n        var currentMedia = checkArr(propsValue) ? propsValue[currentMediaIdx] : propsValue;\n        if (!(currentMedia === null || currentMedia === void 0 ? void 0 : currentMedia.url)) return;\n        var link = document.createElement(\"a\");\n        link.href = currentMedia.url;\n        link.download = currentMedia.name || \"download\";\n        link.target = \"_blank\";\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n    };\n    var handleAction = function(key) {\n        switch(key){\n            case \"add\":\n                handleAdd();\n                break;\n            case \"replace\":\n                handleReplace();\n                break;\n            case \"duplicate\":\n                handleDuplicate();\n                break;\n            case \"remove\":\n                handleRemove();\n                break;\n            case \"download\":\n                handleDownload();\n                break;\n            default:\n                break;\n        }\n    };\n    return {\n        handleAdd: handleAdd,\n        handleReplace: handleReplace,\n        handleDuplicate: handleDuplicate,\n        handleRemove: handleRemove,\n        handleDownload: handleDownload,\n        handleAction: handleAction,\n        handleNextMedia: handleNextMedia,\n        handlePrevMedia: handlePrevMedia\n    };\n};\n_s(useMediaHandlers, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar Media = function(props) {\n    _s1();\n    var _ref = props !== null && props !== void 0 ? props : {}, value = _ref.value, onChange = _ref.onChange, multiple = _ref.multiple;\n    var pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_4__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_6__.PageBuilderContext);\n    var mediaInfoData = context.mediaInfoData, setMediaInfoData = context.setMediaInfoData, activeMediaId = context.activeMediaId, setActiveMediaId = context.setActiveMediaId, layerPos = context.layerPos, setLayerPos = context.setLayerPos, expandedSidebar = context.expandedSidebar;\n    var mediaId = (0,react__WEBPACK_IMPORTED_MODULE_4__.useId)();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_12__._)((0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false), 2), isEdit = _useState[0], setisEdit = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_12__._)((0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(multiple ? value : value), 2), propsValue = _useState1[0], setPropsValue = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_12__._)((0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(checkArr(propsValue) ? propsValue[0] : propsValue), 2), currentMedia = _useState2[0], setCurrentMedia = _useState2[1];\n    var _useState3 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_12__._)((0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0), 2), currentMediaIdx = _useState3[0], setCurrentMediaIdx = _useState3[1];\n    // Use shared media handlers\n    var _useMediaHandlers = useMediaHandlers(propsValue, setPropsValue, currentMediaIdx, setCurrentMediaIdx, multiple, onChange, props.field), handleAdd = _useMediaHandlers.handleAdd, handleReplace = _useMediaHandlers.handleReplace, handleDuplicate = _useMediaHandlers.handleDuplicate, handleRemove = _useMediaHandlers.handleRemove, handleDownload = _useMediaHandlers.handleDownload, handleAction = _useMediaHandlers.handleAction, handleNextMedia = _useMediaHandlers.handleNextMedia, handlePrevMedia = _useMediaHandlers.handlePrevMedia;\n    (0,_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect)(function() {\n        if (checkArr(propsValue)) {\n            setCurrentMedia(propsValue[currentMediaIdx]);\n            setMediaInfoData(propsValue[currentMediaIdx]);\n        } else {\n            setCurrentMedia(propsValue);\n            setMediaInfoData(propsValue);\n        }\n    }, [\n        currentMediaIdx,\n        propsValue\n    ]);\n    // useIsomorphicLayoutEffect(() => {\n    // \tif (isEdit && currentMedia) {\n    // \t\thandleEdit()\n    // \t}\n    // }, [currentMedia])\n    (0,_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect)(function() {\n        mediaInfoData && mediaInfoData.name === \"\" && setisEdit(false);\n    }, [\n        mediaInfoData\n    ]);\n    (0,_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect)(function() {\n        setisEdit(activeMediaId === mediaId);\n    }, [\n        activeMediaId,\n        mediaId\n    ]);\n    var mediaToolbar = [\n        {\n            name: \"Add\",\n            icon: \"add\",\n            action: \"add\",\n            visible: !multiple\n        },\n        {\n            name: \"Replace\",\n            icon: \"replace\",\n            action: \"replace\"\n        },\n        {\n            name: \"Duplicate\",\n            icon: \"duplicate\",\n            action: \"duplicate\",\n            visible: !multiple\n        },\n        {\n            name: \"Remove\",\n            icon: \"remove\",\n            action: \"remove\"\n        },\n        {\n            name: \"Download\",\n            icon: \"download\",\n            action: \"download\",\n            visible: !isEdit\n        }\n    ];\n    var filteredMediaToolbar = mediaToolbar.filter(function(tool) {\n        return !tool.visible;\n    });\n    var handleEdit = function() {\n        setMediaInfoData(currentMedia);\n        setActiveMediaId(mediaId);\n        setLayerPos(props.layerPos);\n    };\n    var handleBack = function() {\n        setActiveMediaId(null);\n        setMediaInfoData({\n            name: \"\",\n            url: \"\"\n        });\n    };\n    var isBuilderMode = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function() {\n        return pathname === null || pathname === void 0 ? void 0 : pathname.startsWith(\"/content-builder/\");\n    }, [\n        pathname\n    ]);\n    (0,_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect)(function() {\n        console.log(layerPos, expandedSidebar);\n        if (layerPos === \"left\" && !expandedSidebar.left || layerPos === \"right\" && !expandedSidebar.right) {\n            setActiveMediaId(null);\n            setMediaInfoData({\n                name: \"\",\n                url: \"\"\n            });\n        }\n    }, [\n        expandedSidebar\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().wrapper),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().controller),\n                style: {\n                    \"--controller-cols\": isBuilderMode ? 12 : 8\n                },\n                children: [\n                    multiple && !isEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().nav),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().nav__btn),\n                                onClick: handlePrevMedia,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                    type: \"cms\",\n                                    variant: \"chevron-left\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 8\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 7\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().nav__index),\n                                children: \"\".concat(currentMediaIdx + 1, \"/\").concat(checkArr(propsValue) ? propsValue.length : 0)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 7\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().nav__btn),\n                                onClick: handleNextMedia,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                    type: \"cms\",\n                                    variant: \"chevron-right\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 8\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 7\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 6\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().body), !isBuilderMode && isEdit ? multiple ? (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().detailed__multi) : (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().detailed) : \"\"),\n                        children: [\n                            currentMedia ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().item),\n                                style: {\n                                    \"--height\": isBuilderMode ? \"160px\" : \"280px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().tag),\n                                        children: formatExt((currentMedia === null || currentMedia === void 0 ? void 0 : currentMedia.ext) || \"\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 8\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().thumbnail),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__.Image, {\n                                            media: currentMedia,\n                                            alt: \"\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 9\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 8\n                                    }, _this),\n                                    !isEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().mask),\n                                        title: \"Edit this media\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                                            onClick: function() {\n                                                return handleEdit();\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                                type: \"cms\",\n                                                variant: \"edit\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 11\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 10\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 9\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 7\n                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().empty),\n                                style: {\n                                    \"--height\": isBuilderMode ? \"160px\" : \"280px\"\n                                },\n                                title: \"Browse file(s)\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                        type: \"cms\",\n                                        variant: \"image\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 8\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"Drop your file(s) here or\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                                                onClick: function() {\n                                                    return handleAction(\"add\");\n                                                },\n                                                children: \"browse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 9\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 8\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                        children: \"Max. File Size: 20MB\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 8\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 7\n                            }, _this),\n                            !isBuilderMode && isEdit && checkArr(propsValue) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items__nav),\n                                        onClick: handlePrevMedia,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                            type: \"cms\",\n                                            variant: \"chevron-left\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 9\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 8\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items__list),\n                                        children: propsValue.map(function(media, idx) {\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items__thumb), idx === currentMediaIdx ? (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().active) : \"\"),\n                                                onClick: function() {\n                                                    return setCurrentMediaIdx(idx);\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__.Image, {\n                                                    media: media,\n                                                    alt: \"\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 11\n                                                }, _this)\n                                            }, idx, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 10\n                                            }, _this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 8\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items__nav),\n                                        onClick: handleNextMedia,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                            type: \"cms\",\n                                            variant: \"chevron-right\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 9\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 8\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 7\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 5\n                    }, _this),\n                    !isBuilderMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__list),\n                                children: filteredMediaToolbar.map(function(tool, idx) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__button),\n                                        onClick: function() {\n                                            return handleAction(tool.action);\n                                        },\n                                        title: tool.name,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                            type: \"cms\",\n                                            variant: tool.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 10\n                                        }, _this)\n                                    }, idx, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 9\n                                    }, _this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 7\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__fixed),\n                                children: !isEdit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__button), (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().text)),\n                                    title: \"Edit\",\n                                    onClick: handleEdit,\n                                    children: \"Edit\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                    lineNumber: 528,\n                                    columnNumber: 9\n                                }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__button), (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().text)),\n                                    title: \"Back\",\n                                    onClick: handleBack,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                        type: \"cms\",\n                                        variant: \"back\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 10\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 9\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 526,\n                                columnNumber: 7\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                        lineNumber: 512,\n                        columnNumber: 6\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                lineNumber: 419,\n                columnNumber: 4\n            }, _this),\n            isEdit && activeMediaId === mediaId && (multiple && checkArr(propsValue) && propsValue.length > 0 || !multiple && propsValue) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MediaInfoLayer__WEBPACK_IMPORTED_MODULE_17__.MediaInfoLayer, {\n                multiple: multiple,\n                toolbar: filteredMediaToolbar,\n                mediaList: propsValue,\n                propsValue: propsValue,\n                setPropsValue: setPropsValue,\n                currentMediaIdx: currentMediaIdx,\n                setCurrentMediaIdx: setCurrentMediaIdx,\n                onChange: onChange,\n                field: props.field\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                lineNumber: 552,\n                columnNumber: 6\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n        lineNumber: 418,\n        columnNumber: 3\n    }, _this);\n};\n_s1(Media, \"8Jn6APO19/8fR3RWDCnchwldD3c=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        react__WEBPACK_IMPORTED_MODULE_4__.useId,\n        useMediaHandlers,\n        _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect,\n        _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect,\n        _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect,\n        _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect\n    ];\n});\n_c = Media;\nvar _c;\n$RefreshReg$(_c, \"Media\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/Media.tsx\n"));

/***/ })

});