"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(dashboard)/content-manager/[...slug]/page",{

/***/ "(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/MediaInfoLayer.tsx":
/*!*****************************************************************************!*\
  !*** ./src/components/Builder/FieldEditor/regular/Media/MediaInfoLayer.tsx ***!
  \*****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MediaInfoLayer: function() { return /* binding */ MediaInfoLayer; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_define_property */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_define_property.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,Input,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,Input,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,Input,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Image/ImageV2.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,Input,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,Input,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Input/Input.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _Media__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Media */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/Media.tsx\");\n/* harmony import */ var _media_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./media.module.scss */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/media.module.scss\");\n/* harmony import */ var _media_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_media_module_scss__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nvar MediaInfoLayer = function(param) {\n    var multiple = param.multiple, toolbar = param.toolbar, mediaList = param.mediaList, propsValue = param.propsValue, setPropsValue = param.setPropsValue, currentMediaIdx = param.currentMediaIdx, setCurrentMediaIdx = param.setCurrentMediaIdx, onChange = param.onChange, field = param.field;\n    _s();\n    var pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_5__.PageBuilderContext);\n    var mediaInfoData = context.mediaInfoData, setMediaInfoData = context.setMediaInfoData, setActiveMediaId = context.setActiveMediaId, layerPos = context.layerPos;\n    var size = mediaInfoData.size, width = mediaInfoData.width, height = mediaInfoData.height, publishedAt = mediaInfoData.publishedAt, ext = mediaInfoData.ext, name = mediaInfoData.name, alternativeText = mediaInfoData.alternativeText, caption = mediaInfoData.caption;\n    var isBuilderMode = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function() {\n        return pathname === null || pathname === void 0 ? void 0 : pathname.startsWith(\"/content-builder/\");\n    }, [\n        pathname\n    ]);\n    // Use shared media handlers\n    var _useMediaHandlers = (0,_Media__WEBPACK_IMPORTED_MODULE_6__.useMediaHandlers)(propsValue, setPropsValue, currentMediaIdx, setCurrentMediaIdx, multiple, onChange, field), handleAction = _useMediaHandlers.handleAction, handleNextMedia = _useMediaHandlers.handleNextMedia, handlePrevMedia = _useMediaHandlers.handlePrevMedia;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_7__._)((0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        size: \"\",\n        dimensions: \"\",\n        date: \"\",\n        extension: \"\"\n    }), 2), fixedInfo = _useState[0], setFixedInfo = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_7__._)((0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        fileName: \"\",\n        altText: \"\",\n        caption: \"\"\n    }), 2), editableInfo = _useState1[0], setEditableInfo = _useState1[1];\n    var handleSave = function() {\n        if (!mediaInfoData || !onChange) return;\n        // Update the current media with edited information\n        var updatedMedia = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_8__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_9__._)({}, mediaInfoData), {\n            name: editableInfo.fileName ? \"\".concat(editableInfo.fileName).concat(mediaInfoData.ext || \"\") : mediaInfoData.name,\n            alternativeText: editableInfo.altText || mediaInfoData.alternativeText,\n            caption: editableInfo.caption || mediaInfoData.caption\n        });\n        // Update propsValue with the modified media\n        if (multiple && (0,_Media__WEBPACK_IMPORTED_MODULE_6__.checkArr)(propsValue)) {\n            var newArray = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_10__._)(propsValue);\n            newArray[currentMediaIdx] = updatedMedia;\n            setPropsValue(newArray);\n            onChange({\n                field: field || \"media\",\n                value: newArray\n            });\n        } else {\n            setPropsValue(updatedMedia);\n            onChange({\n                field: field || \"media\",\n                value: updatedMedia\n            });\n        }\n        // Update context with the new media info\n        setMediaInfoData(updatedMedia);\n        // Close MediaInfoLayer after saving\n        setActiveMediaId(null);\n        setMediaInfoData({\n            name: \"\",\n            url: \"\"\n        });\n    };\n    var handleOnChange = function(e) {\n        var _e_target = e.target, name = _e_target.name, value = _e_target.value;\n        setEditableInfo(function(prev) {\n            return (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_8__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_9__._)({}, prev), (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_11__._)({}, name, value));\n        });\n    };\n    (0,_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_12__.useIsomorphicLayoutEffect)(function() {\n        setFixedInfo({\n            size: \"\".concat(size, \"KB\"),\n            dimensions: \"\".concat(width, \"X\").concat(height),\n            date: (0,_Media__WEBPACK_IMPORTED_MODULE_6__.formatDate)(publishedAt),\n            extension: (0,_Media__WEBPACK_IMPORTED_MODULE_6__.formatExt)(ext || \"\")\n        });\n        setEditableInfo({\n            fileName: name === null || name === void 0 ? void 0 : name.split(\".\").slice(0, -1).join(\".\"),\n            altText: alternativeText || \"\",\n            caption: caption || \"\"\n        });\n    }, [\n        mediaInfoData\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().info), isBuilderMode ? (_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().info__builder) : \"\", layerPos !== \"\" ? (_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default())[layerPos] : \"\"),\n        style: {\n            \"--info-cols\": isBuilderMode ? 12 : 4\n        },\n        children: [\n            isBuilderMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().info__title),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: function() {\n                                    return setMediaInfoData({\n                                        name: \"\",\n                                        url: \"\"\n                                    });\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                                    type: \"cms\",\n                                    variant: \"back\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 8\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 7\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                className: \"collect__heading collect__heading--h6\",\n                                children: \"Media info\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 7\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 6\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().info__media),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().body), multiple ? (_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().detailed__multi) : (_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().detailed)),\n                                children: [\n                                    mediaInfoData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().item),\n                                        style: {\n                                            \"--height\": isBuilderMode ? \"120px\" : \"324px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().tag),\n                                                children: (0,_Media__WEBPACK_IMPORTED_MODULE_6__.formatExt)(mediaInfoData.ext || \"\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 10\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().thumbnail),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_14__.Image, {\n                                                    media: mediaInfoData,\n                                                    alt: \"\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 11\n                                                }, _this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 10\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 9\n                                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().empty),\n                                        style: {\n                                            \"--height\": isBuilderMode ? \"120px\" : \"324px\"\n                                        },\n                                        title: \"Browse file(s)\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                                                type: \"cms\",\n                                                variant: \"image\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 10\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Drop your file(s) here or\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                                                        onClick: function() {\n                                                            return handleAction(\"add\");\n                                                        },\n                                                        children: \"browse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 11\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 10\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                children: \"Max. File Size: 20MB\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 10\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 9\n                                    }, _this),\n                                    Array.isArray(mediaList) && mediaList.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().items),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().items__nav),\n                                                onClick: handlePrevMedia,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                                                    type: \"cms\",\n                                                    variant: \"chevron-left\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 11\n                                                }, _this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 10\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().items__list),\n                                                children: (0,_Media__WEBPACK_IMPORTED_MODULE_6__.checkArr)(mediaList) && mediaList.map(function(media, idx) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().items__thumb), idx === currentMediaIdx ? (_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().active) : \"\"),\n                                                        onClick: function() {\n                                                            return setCurrentMediaIdx(idx);\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_14__.Image, {\n                                                            media: media,\n                                                            alt: \"\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 14\n                                                        }, _this)\n                                                    }, idx, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 13\n                                                    }, _this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 10\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().items__nav),\n                                                onClick: handleNextMedia,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                                                    type: \"cms\",\n                                                    variant: \"chevron-right\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 11\n                                                }, _this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 10\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 9\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 7\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().toolbar),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().toolbar__list),\n                                    children: toolbar.map(function(tool, idx) {\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().toolbar__button),\n                                            onClick: function() {\n                                                return handleAction(tool.action);\n                                            },\n                                            title: tool.name,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                                                type: \"cms\",\n                                                variant: tool.icon\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 11\n                                            }, _this)\n                                        }, idx, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 10\n                                        }, _this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 8\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 7\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 6\n                    }, _this)\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().info__fixed),\n                children: Object.entries(fixedInfo).map(function(param) {\n                    var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_7__._)(param, 2), key = _param[0], value = _param[1];\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().info__fixed_item),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().info__fixed_label),\n                                children: key\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 7\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().info__fixed_value),\n                                children: value\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 7\n                            }, _this)\n                        ]\n                    }, key, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 6\n                    }, _this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                lineNumber: 219,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().info__editable),\n                children: Object.entries(editableInfo).map(function(param) {\n                    var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_7__._)(param, 2), key = _param[0], value = _param[1];\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_4___default().info__editable_item),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                children: key\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 7\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__.Input, {\n                                type: \"text\",\n                                className: \"collect__input has__border\",\n                                name: key,\n                                value: value || \"\",\n                                placeholder: key,\n                                onChange: handleOnChange\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 7\n                            }, _this)\n                        ]\n                    }, key, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 6\n                    }, _this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                lineNumber: 227,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                className: \"collect__button yellow\",\n                onClick: handleSave,\n                children: \"Save\"\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n                lineNumber: 242,\n                columnNumber: 4\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\MediaInfoLayer.tsx\",\n        lineNumber: 113,\n        columnNumber: 3\n    }, _this);\n};\n_s(MediaInfoLayer, \"A6c0wwifVmyOopSc+2zX2QiKCrE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _Media__WEBPACK_IMPORTED_MODULE_6__.useMediaHandlers,\n        _barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_12__.useIsomorphicLayoutEffect\n    ];\n});\n_c = MediaInfoLayer;\nvar _c;\n$RefreshReg$(_c, \"MediaInfoLayer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/MediaInfoLayer.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/layouts/builder/page/LeftSidebarLayout.tsx":
/*!********************************************************!*\
  !*** ./src/layouts/builder/page/LeftSidebarLayout.tsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LeftSidebarLayout: function() { return /* binding */ LeftSidebarLayout; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @swc/helpers/_/_define_property */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_define_property.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Builder__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/Builder */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/FieldEditor.tsx\");\n/* harmony import */ var _components_Builder__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/Builder */ \"(app-pages-browser)/./src/components/Builder/ComponentList/ComponentList.tsx\");\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _LayerSidebarLayout__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./LayerSidebarLayout */ \"(app-pages-browser)/./src/layouts/builder/page/LayerSidebarLayout.tsx\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pagebuilderlayout.module.scss */ \"(app-pages-browser)/./src/layouts/builder/page/pagebuilderlayout.module.scss\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nvar LeftSidebarLayout = function() {\n    var _childComponentData_;\n    _s();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__.PageBuilderContext);\n    var expandedSidebar = context.expandedSidebar, editingIden = context.editingIden, data = context.data, components = context.components, setData = context.setData, setEditingIden = context.setEditingIden, childComponentData = context.childComponentData, setChildComponentData = context.setChildComponentData;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(-1), 2), componentToEditId = _useState[0], setComponentToEditId = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(), 2), cmpData = _useState1[0], setCmpData = _useState1[1];\n    // Filtered component's attributes\n    var filteredComponents = function(obj) {\n        return Object.entries(obj || {}).filter(function(param) {\n            var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)(param, 2), value = _param[1];\n            return typeof value === \"object\" && value !== null;\n        });\n    };\n    // Get sectionData directly from data on each render to always have the latest data\n    var sectionData = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function() {\n        return data === null || data === void 0 ? void 0 : data.data.components.find(function(component) {\n            return component.id === editingIden.id || component.__temp_key__ === editingIden.id;\n        });\n    }, [\n        data,\n        editingIden\n    ]);\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.useIsomorphicLayoutEffect)(function() {\n        setComponentToEditId(editingIden.id);\n    }, [\n        editingIden\n    ]);\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.useIsomorphicLayoutEffect)(function() {\n        setCmpData(components.data.find(function(comp) {\n            return comp.uid === (sectionData === null || sectionData === void 0 ? void 0 : sectionData.__component);\n        }));\n    }, [\n        sectionData,\n        components.data\n    ]);\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.useIsomorphicLayoutEffect)(function() {\n        !expandedSidebar.left && setChildComponentData([]);\n    }, [\n        expandedSidebar.left\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().sidebar), !expandedSidebar.left && (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().is__hidden)),\n        children: cmpData && sectionData && componentToEditId !== -1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__wrapper),\n            children: [\n                childComponentData && ((_childComponentData_ = childComponentData[0]) === null || _childComponentData_ === void 0 ? void 0 : _childComponentData_.name) !== \"\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LayerSidebarLayout__WEBPACK_IMPORTED_MODULE_7__.LayerSidebarLayout, {}, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 67\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__title),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: function() {\n                                return setEditingIden({\n                                    key: \"\",\n                                    id: -1\n                                });\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                type: \"cms\",\n                                variant: \"chevron-left\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 8\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 7\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                            className: \"collect__heading collect__heading--h5\",\n                            children: (cmpData === null || cmpData === void 0 ? void 0 : cmpData.schema.displayName) || cmpData.uid\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 7\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 6\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().editor__components),\n                    children: filteredComponents(cmpData.schema.attributes).map(function(param) {\n                        var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)(param, 2), key = _param[0], value = _param[1];\n                        var val = value;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Builder__WEBPACK_IMPORTED_MODULE_9__.FieldEditor, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, val), {\n                            name: key,\n                            size: 12,\n                            layerPos: \"left\",\n                            value: sectionData[key],\n                            onChange: function(props) {\n                                setData(function(prevData) {\n                                    var newData = (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, prevData);\n                                    var field = props.field, value = props.value;\n                                    var curCmpIndex = newData.data.components.findIndex(function(data) {\n                                        return data.__component === editingIden.key && data.id === editingIden.id;\n                                    });\n                                    if (curCmpIndex === -1) {\n                                        return newData;\n                                    }\n                                    newData.data.components[curCmpIndex] = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, newData.data.components[curCmpIndex]), (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_12__._)({}, field.trim(), value));\n                                    // console.log(`[FieldEditor] New data after update:`, newData)\n                                    return newData;\n                                });\n                            }\n                        }), key, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 9\n                        }, _this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 6\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n            lineNumber: 52,\n            columnNumber: 5\n        }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Builder__WEBPACK_IMPORTED_MODULE_13__.ComponentList, {}, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n            lineNumber: 100,\n            columnNumber: 5\n        }, _this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n        lineNumber: 50,\n        columnNumber: 3\n    }, _this);\n};\n_s(LeftSidebarLayout, \"JQC0MW6nnBgM18/suWAQ8+tf9r8=\", false, function() {\n    return [\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.useIsomorphicLayoutEffect,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.useIsomorphicLayoutEffect,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.useIsomorphicLayoutEffect\n    ];\n});\n_c = LeftSidebarLayout;\nvar _c;\n$RefreshReg$(_c, \"LeftSidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/layouts/builder/page/LeftSidebarLayout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/layouts/builder/page/RightSidebarLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/layouts/builder/page/RightSidebarLayout.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RightSidebarLayout: function() { return /* binding */ RightSidebarLayout; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @swc/helpers/_/_define_property */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_define_property.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_object_without_properties__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_object_without_properties */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_without_properties.js\");\n/* harmony import */ var _swc_helpers_to_property_key__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_to_property_key */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_to_property_key.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Button_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Button_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Button/Button.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Builder__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/Builder */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/FieldEditor.tsx\");\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _LayerSidebarLayout__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./LayerSidebarLayout */ \"(app-pages-browser)/./src/layouts/builder/page/LayerSidebarLayout.tsx\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./pagebuilderlayout.module.scss */ \"(app-pages-browser)/./src/layouts/builder/page/pagebuilderlayout.module.scss\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nvar RightSidebarLayout = function() {\n    var _childComponentData_;\n    _s();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_5__.PageBuilderContext);\n    var expandedSidebar = context.expandedSidebar, contentType = context.contentType, configuration = context.configuration, data = context.data, setData = context.setData, childComponentData = context.childComponentData, setChildComponentData = context.setChildComponentData;\n    var _ref = data || {}, commonData = _ref.data;\n    var router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    var pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    var globalFields = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function() {\n        if (!contentType.data || !configuration.data) return [];\n        var _configuration_data_contentType = configuration.data.contentType, layouts = _configuration_data_contentType.layouts, settings = _configuration_data_contentType.settings;\n        var mainFieldKey = settings.mainField;\n        var _contentType_data_schema_attributes = contentType.data.schema.attributes, mainField = _contentType_data_schema_attributes[mainFieldKey], components = _contentType_data_schema_attributes.components, fields = (0,_swc_helpers_object_without_properties__WEBPACK_IMPORTED_MODULE_6__._)(_contentType_data_schema_attributes, [\n            mainFieldKey,\n            \"components\"\n        ].map(_swc_helpers_to_property_key__WEBPACK_IMPORTED_MODULE_7__._));\n        var normalizedFields = layouts.edit.flat().filter(function(item) {\n            return ![\n                \"components\",\n                mainFieldKey\n            ].includes(item.name);\n        }).map(function(item) {\n            return (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, item, fields[item.name]);\n        }).filter(function(item) {\n            return \"type\" in item;\n        });\n        return normalizedFields;\n    }, [\n        contentType,\n        configuration\n    ]);\n    (0,_barrel_optimize_names_Button_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__.useIsomorphicLayoutEffect)(function() {\n        !expandedSidebar.right && setChildComponentData([]);\n    }, [\n        expandedSidebar.right\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().sidebar), !expandedSidebar.right && (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().is__hidden)),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"collect__button\", \"collect__button--lg\", \"black\"),\n                onClick: function() {\n                    var newPath = pathname.replace(\"content-builder\", \"content-manager\").split(\"/\");\n                    if (newPath[newPath.length - 1] !== \"edit\") {\n                        newPath.push(\"edit\");\n                    }\n                    router.push(newPath.join(\"/\"));\n                },\n                children: \"Switch to Manager Mode\"\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n                lineNumber: 56,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().component__wrapper),\n                children: [\n                    childComponentData && ((_childComponentData_ = childComponentData[0]) === null || _childComponentData_ === void 0 ? void 0 : _childComponentData_.name) !== \"\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LayerSidebarLayout__WEBPACK_IMPORTED_MODULE_11__.LayerSidebarLayout, {}, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 66\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().component__title),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                            className: \"collect__heading collect__heading--h5\",\n                            children: \"Page Settings\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 6\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 5\n                    }, _this),\n                    globalFields === null || globalFields === void 0 ? void 0 : globalFields.map(function(field, idx) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Builder__WEBPACK_IMPORTED_MODULE_12__.FieldEditor, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_13__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, field), {\n                            layerPos: \"right\",\n                            value: commonData && commonData[field.name],\n                            onChange: function(props) {\n                                setData(function(prevData) {\n                                    var newData = (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, prevData);\n                                    var _$field = props.field, value = props.value;\n                                    newData.data = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_13__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, newData.data), (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_14__._)({}, _$field.trim(), value));\n                                    console.log(\"[FieldEditor] New data after update:\", newData);\n                                    return newData;\n                                });\n                            }\n                        }), idx, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 6\n                        }, _this);\n                    })\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n                lineNumber: 68,\n                columnNumber: 4\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n        lineNumber: 55,\n        columnNumber: 3\n    }, _this);\n};\n_s(RightSidebarLayout, \"YtHEQYDdwA8ymYZ75lBB3KCamkk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _barrel_optimize_names_Button_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__.useIsomorphicLayoutEffect\n    ];\n});\n_c = RightSidebarLayout;\nvar _c;\n$RefreshReg$(_c, \"RightSidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/layouts/builder/page/RightSidebarLayout.tsx\n"));

/***/ })

});